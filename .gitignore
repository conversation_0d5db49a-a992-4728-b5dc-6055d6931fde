# Python-generated files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv
venv/
env/

# Environment variables and secrets
.env
.env.local
.env.production
.env.development

# Database files (development only)
users.db
users_settings.db
*.sqlite
*.sqlite3

# Data directory (production persistent storage)
/data/

# Logs
logs/
*.log

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
New Text Document.txt
textbio.txt

docs/
