from fastapi_mail import FastMail, MessageSchema, MessageType
from .mail_config import conf
from fastapi import UploadFile


async def send_authorization_email(recipient: str, verification_url: str):
    html = f"""
    <html>
        <body style="font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 32px;">
            <div style="max-width: 480px; margin: auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.07); padding: 32px 24px;">
                <h2 style="color: #1a73e8; margin-top: 0;">Welcome to <b>Hydr-Apps</b>!</h2>
                <p style="font-size: 1.1em; color: #333;"><b>Thank you for signing up!</b></p>
                <p style="color: #444;">To complete your registration, please verify your email address by clicking the button below:</p>
                <div style="text-align: center; margin: 32px 0;">
                    <a href="{verification_url}" style="background: #1a73e8; color: #fff; text-decoration: none; padding: 14px 32px; border-radius: 6px; font-weight: bold; font-size: 1.1em; display: inline-block; box-shadow: 0 2px 4px rgba(26,115,232,0.12);">Verify Email Address</a>
                </div>
                <p style="font-size: 0.97em; color: #888;">If you did not create an account, you can safely ignore this email.</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 32px 0 16px 0;">
                <p style="font-size: 0.9em; color: #bbb; text-align: center;">&copy; {2025} Hydr-Apps. All rights reserved.</p>
            </div>
        </body>
    </html>
    """
    message = MessageSchema(
        subject="Welcome to Hydr-Apps! Please Verify Your Email Address",
        recipients=[recipient],
        body=html,
        subtype=MessageType.html
    )
    fm = FastMail(conf)
    await fm.send_message(message)

async def send_reset_email(recipient: str, reset_url: str):
    html = f"""
    <html>
        <body style=\"font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 32px;\">
            <div style=\"max-width: 480px; margin: auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.07); padding: 32px 24px;\">
                <h2 style=\"color: #e8711a; margin-top: 0;\"><b>Password Reset Request</b></h2>
                <p style=\"font-size: 1.1em; color: #333;\"><b>Hello,</b></p>
                <p style=\"color: #444;\">We received a request to reset your password. Click the button below to set a new password for your Hydr-Apps account:</p>
                <div style=\"text-align: center; margin: 32px 0;\">
                    <a href=\"{reset_url}\" style=\"background: #e8711a; color: #fff; text-decoration: none; padding: 14px 32px; border-radius: 6px; font-weight: bold; font-size: 1.1em; display: inline-block; box-shadow: 0 2px 4px rgba(232,113,26,0.12);\">Reset Password</a>
                </div>
                <p style=\"font-size: 0.97em; color: #888;\">If you did not request a password reset, you can safely ignore this email.</p>
                <hr style=\"border: none; border-top: 1px solid #eee; margin: 32px 0 16px 0;\">
                <p style=\"font-size: 0.9em; color: #bbb; text-align: center;\">&copy; {2025} Hydr-Apps. All rights reserved.</p>
            </div>
        </body>
    </html>
    """
    message = MessageSchema(
        subject="Hydr-Apps Password Reset Instructions",
        recipients=[recipient],
        body=html,
        subtype=MessageType.html
    )
    fm = FastMail(conf)
    await fm.send_message(message)

async def send_report_email(recipient: str, upload_file: UploadFile, filename: str):
    html = f"""
    <html>
        <body style=\"font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 32px;\">
            <div style=\"max-width: 480px; margin: auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.07); padding: 32px 24px;\">
                <h2 style=\"color: #28a745; margin-top: 0;\"><b>Your Calculation Report is Ready!</b></h2>
                <p style=\"font-size: 1.1em; color: #333;\"><b>Hello,</b></p>
                <p style=\"color: #444;\">Please find your requested calculation report attached to this email.</p>
                <div style=\"text-align: center; margin: 32px 0;\">
                    <span style=\"display: inline-block; background: #28a745; color: #fff; padding: 10px 28px; border-radius: 6px; font-weight: bold; font-size: 1.05em; box-shadow: 0 2px 4px rgba(40,167,69,0.12);\">Calculation Report: {filename}</span>
                </div>
                <p style=\"font-size: 0.97em; color: #888;\">If you have any questions or need further assistance, please reply to this email.</p>
                <hr style=\"border: none; border-top: 1px solid #eee; margin: 32px 0 16px 0;\">
                <p style=\"font-size: 0.9em; color: #bbb; text-align: center;\">&copy; {2025} Hydr-Apps. All rights reserved.</p>
            </div>
        </body>
    </html>
    """
    message = MessageSchema(
        subject=f"Your Hydr-Apps Calculation Report is Attached",
        recipients=[recipient],
        body=html,
        subtype=MessageType.html,
        attachments=[upload_file]
    )
    fm = FastMail(conf)
    await fm.send_message(message)

