{% extends "base.jinja2" %}

{% block title %}Calc01{% endblock %}
{% block content %}
  <div id="content-div">

    {# TITLE SINGLE #}
    <div>
      <style>
          me {
            width: 100%;
            text-align: center;
            padding-bottom: 48px;
          }

          me div {
            color: var(--color-text-title);
            padding-bottom: 14px;
            font-family: 'Noto Serif', serif;
            font-size: 24px;
            font-weight: 400;
            font-stretch: semi-condensed;
            font-style: italic;
          }

          me hr {
            display: block;
            height: 1px;
            border: 0;
            border-top: 1px solid var(--color-hr-lines);
            margin: 0;
            padding: 0;
          }
      </style>
      <div>-(5) Calculation Title</div>
      <hr />
    </div>

    <form data-signals="{_users_submit_button_disable:false, _form_invalid:false}" data-on-submit="$_users_submit_button_disable = true;@post('/calc01_submit', {contentType: 'form'})">

      <div data-on-input="let PE = document.getElementById('PE'); let sp_flow = document.getElementById('sp_flow'); let sp_BOD5 = document.getElementById('sp_BOD5'); let sp_COD = document.getElementById('sp_COD'); let sp_SS = document.getElementById('sp_SS'); let sp_TN = document.getElementById('sp_TN'); let sp_P = document.getElementById('sp_P'); let safety_factor = document.getElementById('safety_factor'); let svi = document.getElementById('svi'); let primary = document.getElementById('primary'); let primary_duration = document.getElementById('primary_duration'); let sludge_storage_duration = document.getElementById('sludge_storage_duration'); let wwtp_type = document.getElementById('wwtp_type'); let separate_deintrification_chamber = document.getElementById('separate_deintrification_chamber'); let qsv = document.getElementById('qsv'); $_form_invalid = !(PE?.checkValidity() && sp_flow?.checkValidity() && sp_BOD5?.checkValidity() && sp_COD?.checkValidity() && sp_SS?.checkValidity() && sp_TN?.checkValidity() && sp_P?.checkValidity() && safety_factor?.checkValidity() && svi?.checkValidity() && primary?.checkValidity() && primary_duration?.checkValidity() && sludge_storage_duration?.checkValidity() && wwtp_type?.checkValidity() && separate_deintrification_chamber?.checkValidity() && qsv?.checkValidity())" data-on-change="let PE = document.getElementById('PE'); let sp_flow = document.getElementById('sp_flow'); let sp_BOD5 = document.getElementById('sp_BOD5'); let sp_COD = document.getElementById('sp_COD'); let sp_SS = document.getElementById('sp_SS'); let sp_TN = document.getElementById('sp_TN'); let sp_P = document.getElementById('sp_P'); let safety_factor = document.getElementById('safety_factor'); let svi = document.getElementById('svi'); let primary = document.getElementById('primary'); let primary_duration = document.getElementById('primary_duration'); let sludge_storage_duration = document.getElementById('sludge_storage_duration'); let wwtp_type = document.getElementById('wwtp_type'); let separate_deintrification_chamber = document.getElementById('separate_deintrification_chamber'); let qsv = document.getElementById('qsv'); $_form_invalid = !(PE?.checkValidity() && sp_flow?.checkValidity() && sp_BOD5?.checkValidity() && sp_COD?.checkValidity() && sp_SS?.checkValidity() && sp_TN?.checkValidity() && sp_P?.checkValidity() && safety_factor?.checkValidity() && svi?.checkValidity() && primary?.checkValidity() && primary_duration?.checkValidity() && sludge_storage_duration?.checkValidity() && wwtp_type?.checkValidity() && separate_deintrification_chamber?.checkValidity() && qsv?.checkValidity())">

      {{ render_partial('partials/forms-select-i.jinja2',
          namealwayschange='select_customer',
          required=False,
          label='Select Customer',
          optionslist=customer_names,
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext='The info of the selected customer will be showed on the pdf report.'
            ))
      }}


      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='PE',
          label='Population Equivalent',
          type = 'text',
          pattern="1000|[5-9]|[1-9][0-9]|[1-9][0-9]{2}",
          errormessage="Enter an integer between 5 and 1000",
          value="100",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="(Ισοδύναμος) Πληθυσμός που φορτίζει τον βιολογικό καθαρισμό",
            infotitle="Population Equivalent",
            infounit="Equivalent Population",
            inforange="Integer, 5-1000"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='sp_flow',
          label='Qin per PE',
          type = 'text',
          pattern="^(0|[1-9]|[1-9][0-9]|1[0-9]{2}|2[0-9]{2}|300)$",
          errormessage="Enter an integer between 0 and 300",
          value="150",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Ημερήσια παροχή λυμάτων ενός ισοδύναμου κατοίκου",
            infotitle="Qin per PE",
            infounit="L/PE/d",
            inforange="Integer, 0-300"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='sp_BOD5',
          label='BOD<sub>5</sub>',
          type = 'text',
          pattern="^((1[0-9]|[2-8][0-9]|90)(\.\d+)?|10|90)$",
          errormessage="Please enter a floating-point number between 10 and 90",
          value="60",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Βιολογικώς απαιτούμενο οξυγόνο ανά ισοδύναμο κάτοικο και ημέρα",
            infotitle="BOD<sub>5</sub>",
            infounit="gr/PE/d",
            inforange="float, 10-90"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='sp_COD',
          label='COD',
          type = 'text',
          pattern="^(1[0-7][0-9](\.\d+)?|180(\.0+)?|[2-9][0-9](\.\d+)?|20(\.0+)?)$",
          errormessage="Enter a floating point number between 20 and 180.",
          value="120",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Χημικώς απαιτούμενο οξυγόνο ανά ισοδύναμο κάτοικο και ημέρα",
            infotitle="COD",
            infounit="gr/PE/d",
            inforange="float, 20-180"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='sp_SS',
          label='Suspended Solids',
          type = 'text',
          pattern="^(90(\.0+)?|[3-8][5-9](\.\d+)?|[4-8][0-9](\.\d+)?|35(\.0+)?|[3-8][6-9](\.\d+)?)$",
          errormessage="Enter a number between 35 and 90 (decimals allowed).",
          value="70",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Αιωρούμενα στερεά ανά ισοδύναμο κάτοικο και ημέρα",
            infotitle="Suspended Solids",
            infounit="gr/PE/d",
            inforange="float, 35-90"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='sp_TN',
          label='Total Nitrogen',
          type = 'text',
          pattern="^(0|[1-9]\d*)(\.\d+)?$",
          errormessage="Enter a number greater than or equal to 0.",
          value="11",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Ολικό άζωτο ανά ισοδύναμο κάτοικο και ημέρα",
            infotitle="Total Nitrogen",
            infounit="gr/PE/d",
            inforange="float, ≥0"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='sp_P',
          label='Phosphorus',
          type = 'text',
          pattern="^(0|[1-9]\d*)(\.\d+)?$",
          errormessage="Enter a number greater than or equal to 0.",
          value="1.8",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Φώσφορος ανά ισοδύναμο κάτοικο και ημέρα",
            infotitle="Phosphorus",
            infounit="gr/PE/d",
            inforange="float, ≥0"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='safety_factor',
          label='Safety Factor',
          type = 'text',
          pattern="^([1-9]\d*)(\.\d+)?$",
          errormessage="Enter a number greater than or equal to 1.",
          value="1.8",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Συντελεστής ασφάλειας",
            infotitle="Safety Factor",
            infounit="-",
            inforange="float, ≥1"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='svi',
          label='Sludge Volume Index',
          type = 'text',
          pattern="^-?\d+$",
          errormessage="Enter an integer.",
          value="100",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Δείκτης καθίζησης λάσπης",
            infotitle="Sludge Volume Index",
            infounit="L/kg",
            inforange="Integer"
            ))
      }}


      {{ render_partial('partials/forms-select-i.jinja2',
          namealwayschange='primary',
          label='Is there Primary Treatment?',
          optionslist = [
            {'value': 'True', 'selected': False},
            {'value': 'False', 'selected': True}
          ],
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Ύπαρξη ή όχι πρωτοβάθμιας επεξεργασίας (συνδέεται με το tpr)",
            infotitle="Is there Primary Treatment?"
            ))
      }}

      {{ render_partial('partials/forms-select-i.jinja2',
          namealwayschange='primary_duration',
          label='Primary Duration',
          optionslist = [
            {'value': '0.0', 'selected': False},
            {'value': '0.5', 'selected': False},
            {'value': '1.0', 'selected': False},
            {'value': '1.5', 'selected': False},
            {'value': '2.0', 'selected': False},
          ],
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Διάρκεια πρωτοβάθμιας επεξεργασίας<br><br>0 if there is no Primary Treatment.",
            infotitle="Primary Duration",
            infounit="Hours"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='sludge_storage_duration',
          label='Sludge Storage Duration',
          type='text',
          pattern="^(0|[1-9]|1[0-2])$",
          errormessage="Enter an integer between 0 and 12.",
          value="6",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Διάρκεια αποθήκευσης λάσπης στην δεξαμενή πρωτοβάθμιας επεξεργασίας",
            infotitle="Sludge Storage Duration",
            infounit="Months",
            inforange="Integer, 0-12"
            ))
      }}

      {{ render_partial('partials/forms-select-i.jinja2',
          namealwayschange='wwtp_type',
          label='Treatment Type',
          optionslist = [
            {'value': 'ORG. C', 'selected': False},
            {'value': 'NITR', 'selected': False},
            {'value': 'DENITR', 'selected': False},
            {'value': 'STAB', 'selected': True},
          ],
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Είδος επεξεργασίας (τύπος) βιολογικού καθαρισμού: 'BOD5 (organic) removal', 'Nitrification procedure', 'Denitrification procedure', 'Sludge Stabilization', 'Phosphorus Removal'",
            infotitle="Treatment Type"
            ))
      }}

      {{ render_partial('partials/forms-select-i.jinja2',
          namealwayschange='separate_deintrification_chamber',
          label='Is dentrification chamber separated from reactor',
          optionslist = [
            {'value': 'True', 'selected': False},
            {'value': 'False', 'selected': True}
          ],
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Αν η απονιτροποίηση γίνεται σε ξεχωριστό (απομονωμένο) θάλαμο ή μέσα στην δεξαμενή του αντιδραστήρα",
            infotitle="Is dentrification chamber separated from reactor"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          namealwayschange='qsv',
          label='q<sub>sv</sub>',
          type='text',
          pattern="^(650(\.0+)?|([0-9]{1,2}|[1-5][0-9]{2}|6[0-4][0-9]|[1-9][0-9]?)(\.\d+)?|0(\.0+)?)$",
          errormessage="Enter a number less than or equal to 650.",
          value="650",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Ρυθμός επιφανειακής φόρτισης όγκου λάσπης της δεξαμενής δευτεροβάθμιας καθίζησης. Καθίζηση μάζας cm/h",
            infotitle="q<sub>sv</sub>",
            infounit="l/(m<sup>2</sup>*h)",
            inforange="Float, ≤650"
            ))
      }}

      </div>

      {# SUBMIT BUTTON #}
      <button type="submit" data-attr-disabled="$_form_invalid || $_users_submit_button_disable">
        <style>
            me {
                height: 40px;
                margin-top: 28px;
                margin-bottom: 42px;
                width: 100%;
                display: block;
                background-color: transparent;
                color: var(--color-text-dark);
                border: 1px solid var(--color-text-dark);
                cursor: pointer;
                border-width: 1px;
                border-radius: 6px;
                font-family: 'Noto Sans', sans-serif;
                font-weight: 500;
                font-size: 18px;
                font-stretch: semi-condensed;
                text-align: center;
                transition: background-color 0.3s ease, color 0.3s ease;
            }

            me:hover {
                background-color: var(--color-background-dark);
                color: var(--color-text-bright);
            }

            me:disabled {
                background-color: var(--color-disabled-background);
                color: var(--color-text-black);
                opacity: 0.6;
                cursor: not-allowed;
            }

            me .button-spinner {
                display: none;
                width: 30px;
                height: 30px;
            }
        </style>
        <span class="button-text" data-attr-style="$_users_submit_button_disable ? 'display: none' : 'display: inline'">Submit</span>
        <img class="button-spinner" data-attr-style="$_users_submit_button_disable ? 'display: inline; margin-top: 4px; margin-bottom: 0px;' : 'display: none'"
              src="{{ url_for('static', path='/images/tube-spinner.svg') }}"
              alt="spinning..." />
      </button>
    </form>

    <div id="errordiv"></div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
      const primarySelect = document.getElementById('primary');
      const primaryDurationSelect = document.getElementById('primary_duration');

      function updatePrimaryDuration() {
        if (primarySelect.value === 'False') {
          primaryDurationSelect.value = '0.0';
          primaryDurationSelect.disabled = true;
          primaryDurationSelect.setAttribute('value', '0.0');
          // Trigger the label update logic in the partial
          primaryDurationSelect.dispatchEvent(new Event('change', { bubbles: true }));
        } else {
          primaryDurationSelect.disabled = false;
          // Trigger the label update logic in the partial
          primaryDurationSelect.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }

      // Initial state
      updatePrimaryDuration();

      // Listen for changes
      primarySelect.addEventListener('change', updatePrimaryDuration);
    });
    </script>

  </div>
{% endblock content %}