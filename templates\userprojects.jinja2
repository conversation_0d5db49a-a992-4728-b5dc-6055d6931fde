{% extends "base.jinja2" %}

{% block title %}Projects{% endblock %}
{% block content %}
<div id="content-div">
  <style>
      me {
          position: relative;
          width: 100%;
      }
  </style>

  {# X BUTTON (TOP RIGHT) #}
  <div onclick="location.href='/calculations'">
    <style>
      me {
          position: absolute;
          top: -30px;
          right: clamp(-150px, -31%, -18px);
          width: 32px;
          height: 32px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
      }

        me .close-x {
            width: 26px;
            height: 2px;
            background-color: var(--color-text-dark);
            position: relative;
            transform: rotate(45deg);
            transition: all 0.3s ease;
        }

        me .close-x::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 2px;
            background-color: var(--color-text-dark);
            left: 0;
            top: 0;
            transform: rotate(-90deg);
            transition: all 0.3s ease;
        }

        me:hover .close-x,
        me:hover .close-x::after {
            background-color: var(--color-hamburger-hover);
        }
    </style>
    <div class="close-x"></div>
  </div>

  {# TITLE DOUBLE #}
  <div>
    <style>
        me {
          width: 100%;
          text-align: center;
          padding-bottom: 8px;
        }

        me hr {
          display: block;
          height: 1px;
          border: 0;
          border-top: 1px solid var(--color-hr-lines);
          margin-top: 10;
          padding: 0;
        }
    </style>
    <div>
      Projects
      <style>
          me {
            color: var(--color-text-title);
            padding-bottom: 28px;
            font-family: 'Noto Serif', serif;
            font-size: 30px;
            font-weight: 400;
            font-stretch: semi-condensed;
            font-style: italic;
          }
      </style>
    </div>
    <div>
      <style>
          me {
            color: var(--color-text-subtitle);
            padding-bottom: 2px;
            font-family: "Noto Sans", sans-serif;
            font-size: 20px;
            font-weight: 300;
            font-stretch: semi-condensed;
            font-style: normal;
          }
      </style>
      Select a project to start a new calculation<br>with pre-occupied input fields.
    </div>
    <hr />
  </div>


  <div id="errordiv"></div>

  <div>
    <style>
        me {
          margin-top: 99px;
          margin-bottom: 0px;
          text-align: center;
        }

        me a {
          color: var(--color-text-dark);
          text-decoration: none;
          font-weight: 400;
          font-stretch: semi-condensed;
        }

        me a:hover {
          text-decoration: underline;
          cursor: pointer;
        }
    </style>
    Implementation forthcoming...
  </div>

</div>

{% endblock content %}

